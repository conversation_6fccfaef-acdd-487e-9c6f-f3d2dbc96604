const _ = require("lodash");
const moment = require("moment");
const utility = require("utility");
const pinyin = require("node-pinyin");
const i18n = require("../config/i18n_cn");
const RetJson = require("../proxy/retjson");
const commFunc = require("../lib/common/comm_func");
const validator = require("../lib/common/validator_extend");

const userDao = require("../lib/dao/uc_user_dao");
const roleDao = require("../lib/dao/uc_role_dao");
const organDao = require("../lib/dao/uc_organ_dao");
const payOrderDao = require("../lib/dao/pay_order_dao");
const cardAuthGroupDao = require("../lib/dao/card/auth_group");
const cardAuthGroupAccessDao = require("../lib/dao/card/auth_group_access_dao");
async function getUserLogic(queryParams) {
  const rules = [
    {
      field: "userId",
      title: "用户Id",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
      opt: "u",
    }
  ];

  const [err, model] = commFunc.checkRequestData(queryParams, rules);
  if (err) {
    return [err];
  }

  const user = await userDao.getUserSql(queryParams);
  // 当前车组信息
  const organ = await organDao.getOrganSql({
    organId: user.organId
  });
  user.organ = organ
  // 上级车组下的主账号信息
  user.superiorMainUser = null
  let superiorOrganId = 0;
  if (user.type === 1) {    // 当前用户是主账号, 他的上级是上级车组的主账号
    superiorOrganId = organ.parentId;
  } else {    // 当前用户是子账号, 他的上级是当前车组的主账号
    superiorOrganId = user.organId;
  }
  if (superiorOrganId > 0) {
    // 获取上级车组主账号信息
    const superiorMainUser = await userDao.getUserSql({
      organId: superiorOrganId,
      type: 1
    });
    user.superiorMainUser = superiorMainUser
  }
  // 分润余额(待提现的金额)
  const userDivisionAmount = await payOrderDao.getUserDivisionAmountSql({
    userId: queryParams.userId,
    status: 1
  })
  user.divisionAmount = userDivisionAmount.amount || 0;
  return user;
}

async function getUserListLogic(queryParams, currentUser) {
  const [userList, roleList] = await Promise.all([
    userDao.getUserSqlList(queryParams, currentUser),
    roleDao.getRoleDropDownSqlList({ status: -1, organId: 0 }),
  ]);
  for (const e of userList.rows) {
    const i = userList.rows.indexOf(e);
    const compactRoles = _.compact((e.roleIds || "").split(","));
    const userRoles = compactRoles.map((roleId) =>
      _.result(
        roleList.find((f) => `${f.roleId}` === `${roleId}`),
        "roleName"
      )
    );

    e.index = queryParams.offset + i + 1;
    e.roleNames = userRoles.join(",");
    e.roleIds = e.roleIds.substring(1, e.roleIds.length - 1);
    e.strOrganName = `${"-".repeat((e.level - 1) * 2)}${e.organName}`;
    e.remark = e.remark || "";
    e.createTime = e.createTime
      ? moment(e.createTime).format("YYYY-MM-DD HH:mm")
      : "";
    e.lastLoginTime = e.lastLoginTime
      ? moment(e.lastLoginTime).format("YYYY-MM-DD HH:mm")
      : "";
    // 当前车组信息
    const organ = await organDao.getOrganSql({
      organId: e.organId
    });
    e.organAddress = organ ? organ.address : "";
  };

  return userList;
}

async function getUserDropDownListLogic(queryParams) {
  const queryRoles = _.compact((queryParams.strRoleId || "").split(","));
  const [userDropDownList, roleList] = await Promise.all([
    userDao.getUserDropDownSqlList(queryParams, queryRoles),
    roleDao.getRoleDropDownSqlList({ status: -1, organId: 0 }),
  ]);

  userDropDownList.forEach((e) => {
    const compactRoles = _.compact((e.roleIds || "").split(","));
    const userRoles = compactRoles.map((roleId) =>
      _.result(
        roleList.find((f) => `${f.roleId}` === `${roleId}`),
        "roleName"
      )
    );
    e.roleNames = userRoles.join(",");
    e.roleIds = e.roleIds.substring(1, e.roleIds.length - 1);
  });

  return userDropDownList;
}

function _validateUserRequest(requestData) {
  requestData.userName = (requestData.userName || "").trim();
  requestData.loginName = (requestData.loginName || "").trim();
  requestData.realname = (requestData.realname || "").trim();

  const rules = [
    {
      field: "userId",
      title: "用户Id",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
      opt: "u",
    },
    { field: "userName", title: "用户名", rule: "", msg: "" },
    {
      field: "loginName",
      title: "登录手机",
      rule: "",
      msg: "",
      required: true,
    },
    { field: "realname", title: "姓名", rule: "", msg: "" },
    { field: "sex", title: "性别", rule: "isIntFormat", msg: "" },
    {
      field: "strRoleId",
      title: "角色Id",
      rule: "isCommaSeparated",
      msg: "",
      required: true,
    },
    {
      field: "organId",
      title: "所属机构",
      rule: [{ name: "isIntFormat", opt: { min: 1 } }],
      msg: "",
      required: true,
    },
    { field: "email", title: "邮箱", rule: "isEmail", msg: "" },
    {
      field: "isSuper",
      title: "是否超级管理员",
      rule: "isInt01",
      msg: "",
      required: true,
    },
    {
      field: "status",
      title: "状态",
      rule: "isInt01",
      msg: "",
      required: true,
    },
  ];

  const [err, model] = commFunc.checkRequestData(requestData, rules);
  if (err) {
    return [err];
  }

  model.sex = model.sex || 0;
  model.email = model.email || "";
  const strRoleId = _.compact((model.strRoleId || "").split(","))
    .sort()
    .join(",");
  model.roleIds = `,${strRoleId},`;

  delete model.strRoleId;
  return [undefined, model];
}

async function insertUserLogic(insertData, currentUser) {
  insertData.opt = "i";
  const [err, userModel] = _validateUserRequest(insertData);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  const isExists = await userDao.getUserRecordsSql(userModel);
  if (isExists.record) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.RESPONSE_DATA_ALREADY_EXISTS);
  }

  userModel.password = utility.md5("000000");
  userModel.createUser = currentUser.loginName;
  userModel.isSuper = currentUser.isSuper ? userModel.isSuper : 0;

  const operationModel = {
    userId: currentUser.userId,
    realname: currentUser.realname,
    organId: currentUser.organId,
    operationType: "新增用户",
    content: `新增用户 ${userModel.realname}, 账号：${userModel.loginName} `,
  };
  return await userDao.insertUserSql(userModel, operationModel);
}

async function updateUserLogic(updateData, currentUser) {
  updateData.opt = "u";
  const [err, userModel] = _validateUserRequest(updateData);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  const isExists = await userDao.getUserRecordsSql(userModel);
  if (isExists.record && isExists.record > 1) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.RESPONSE_DATA_OVERTWO_EXISTS);
  }
  if (
    Number.parseInt(userModel.userId, 10) !== isExists.userId &&
    isExists.record
  ) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.RESPONSE_DATA_ALREADY_EXISTS);
  }

  userModel.updateUser = currentUser.loginName;
  userModel.isSuper = currentUser.isSuper ? userModel.isSuper : -1;

  const operationModel = {
    userId: currentUser.userId,
    realname: currentUser.realname,
    organId: currentUser.organId,
    operationType: "编辑用户",
    content: `编辑用户 ${userModel.realname}, 账号：${userModel.loginName} `,
  };
  await userDao.updateUserSql(userModel, operationModel);
}

async function resetPasswordLogic(resetData) {
  if (!validator.isIntFormat(resetData.userId, { min: 1 })) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.REQUEST_INPUT_ID_ERROR);
  }

  const userData = await userDao.getUserByIdSqlData({
    searchKey: resetData.userId,
  });
  if (!userData) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.RESPONSE_DATA_NOT_FOUND);
  }

  resetData.password = utility.md5("000000");
  await userDao.updateUserPasswordSql(resetData);
}

async function updateUserPasswordLogic(updateData) {
  const rules = [
    {
      field: "loginName",
      title: "用户账户",
      rule: [{ name: "isLengthCorrect", opt: { min: 2 } }],
      msg: "",
      required: true,
    },
    {
      field: "oldPswd",
      title: "旧密码",
      rule: [{ name: "isLengthCorrect", opt: { min: 2 } }],
      msg: "",
      required: true,
    },
    {
      field: "newPswd",
      title: "新密码",
      rule: [{ name: "isLengthCorrect", opt: { min: 2 } }],
      msg: "",
      required: true,
    },
  ];

  const [err, updateModel] = commFunc.checkRequestData(updateData, rules);
  if (err) {
    throw new RetJson(i18n.SYS_ERROR_CODE, err);
  }

  if (updateModel.newPswd === "123456") {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.ACCOUNT_PASSWORD_SIMPLE);
  }

  const userData = await userDao.getUserByIdSqlData({
    searchKey: updateModel.loginName,
  });
  if (!userData) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.SIGN_IN_USER_NOT_EXIST);
  }

  if (updateModel.oldPswd !== userData.password) {
    throw new RetJson(i18n.SYS_ERROR_CODE, i18n.SIGN_IN_PASSWORD_ERROR);
  }

  updateModel.password = utility.md5(updateModel.newPswd);
  updateModel.userId = userData.userId;
  await userDao.updateUserPasswordSql(updateModel);
}

async function batchInsertUserTestLogic(userName, gCount, ucRoleId, organId) {
  function getName() {
    const familyNames = [
      "赵",
      "钱",
      "孙",
      "李",
      "周",
      "吴",
      "郑",
      "王",
      "冯",
      "陈",
      "褚",
      "卫",
      "蒋",
      "沈",
      "韩",
      "杨",
      "朱",
      "秦",
      "尤",
      "许",
      "何",
      "吕",
      "施",
      "张",
      "孔",
      "曹",
      "严",
      "华",
      "金",
      "魏",
      "陶",
      "姜",
      "戚",
      "谢",
      "邹",
      "喻",
      "柏",
      "水",
      "窦",
      "章",
      "云",
      "苏",
      "潘",
      "葛",
      "奚",
      "范",
      "彭",
      "郎",
      "鲁",
      "韦",
      "昌",
      "马",
      "苗",
      "凤",
      "花",
      "方",
      "俞",
      "任",
      "袁",
      "柳",
      "酆",
      "鲍",
      "史",
      "唐",
      "费",
      "廉",
      "岑",
      "薛",
      "雷",
      "贺",
      "倪",
      "汤",
      "滕",
      "殷",
      "罗",
      "毕",
      "郝",
      "邬",
      "安",
      "常",
      "乐",
      "于",
      "时",
      "傅",
      "皮",
      "卞",
      "齐",
      "康",
      "伍",
      "余",
      "元",
      "卜",
      "顾",
      "孟",
      "平",
      "黄",
      "和",
      "穆",
      "萧",
      "尹",
    ];

    const givenNames = [
      "子璇",
      "淼",
      "国栋",
      "夫子",
      "瑞堂",
      "甜",
      "敏",
      "尚",
      "国贤",
      "贺祥",
      "晨涛",
      "昊轩",
      "易轩",
      "益辰",
      "益帆",
      "益冉",
      "瑾春",
      "瑾昆",
      "春齐",
      "杨",
      "文昊",
      "东东",
      "雄霖",
      "浩晨",
      "熙涵",
      "溶溶",
      "冰枫",
      "欣欣",
      "宜豪",
      "欣慧",
      "建政",
      "美欣",
      "淑慧",
      "文轩",
      "文杰",
      "欣源",
      "忠林",
      "榕润",
      "欣汝",
      "慧嘉",
      "新建",
      "建林",
      "亦菲",
      "林",
      "冰洁",
      "佳欣",
      "涵涵",
      "禹辰",
      "淳美",
      "泽惠",
      "伟洋",
      "涵越",
      "润丽",
      "翔",
      "淑华",
      "晶莹",
      "凌晶",
      "苒溪",
      "雨涵",
      "嘉怡",
      "佳毅",
      "子辰",
      "佳琪",
      "紫轩",
      "瑞辰",
      "昕蕊",
      "萌",
      "明远",
      "欣宜",
      "泽远",
      "欣怡",
      "佳怡",
      "佳惠",
      "晨茜",
      "晨璐",
      "运昊",
      "汝鑫",
      "淑君",
      "晶滢",
      "润莎",
      "榕汕",
      "佳钰",
      "佳玉",
      "晓庆",
      "一鸣",
      "语晨",
      "添池",
      "添昊",
      "雨泽",
      "雅晗",
      "雅涵",
      "清妍",
      "诗悦",
      "嘉乐",
      "晨涵",
      "天赫",
      "玥傲",
      "佳昊",
      "天昊",
      "萌萌",
      "若萌",
    ];

    const i =
      Number.parseInt(10 * Math.random(), 10) * 10 +
      Number.parseInt(10 * Math.random(), 10);
    const familyName = familyNames[i];

    const j =
      Number.parseInt(10 * Math.random(), 10) * 10 +
      Number.parseInt(10 * Math.random(), 10);
    const givenName = givenNames[j];

    return familyName + givenName;
  }

  function getMoble() {
    const prefixArray = [
      "130",
      "131",
      "132",
      "133",
      "135",
      "137",
      "138",
      "170",
      "187",
      "189",
    ];
    const i = Number.parseInt(10 * Math.random(), 10);
    let prefix = prefixArray[i];

    for (let j = 0; j < 8; j++) {
      prefix += Math.floor(Math.random() * 10);
    }
    return prefix;
  }

  function getUserCode() {
    const arr = [];
    for (let i = 0; i < 5; i++) {
      let num = Math.random() * 9;
      num = parseInt(num, 10);
      arr.push(num);
    }
    return arr.join("");
  }

  const insertList = [];
  for (let i = 0; i < gCount; i++) {
    const realname = getName();
    const pinyinName = (pinyin(realname, { style: "normal" }) || [])
      .map((m) => m.join(""))
      .join("");
    // const randomNum = commFunc.randomPassword();
    const password = utility.md5("000000");

    const mobile = getMoble();

    insertList.push({
      userCode: `2${getUserCode()}`,
      userName: pinyinName,
      loginName: mobile,
      password,
      realname: realname,
      roleIds: `,${ucRoleId},`,
      organId,
      email: `${mobile}@qq.com`,
      isEmployee: 0,
      createUser: userName,
    });
  }

  await userDao.batchInsertUserSql(insertList);
}
// 同步用户角色关系到卡系统
async function asyncUserRolesToCardSystem(userId, roleIds) {
  let roleList = await roleDao.getRolesListInIds(roleIds);
  let groupList = await cardAuthGroupDao.getGroupInNames(roleList.map((m) => m.roleName));
  await cardAuthGroupAccessDao.updateUserGroup(userId, groupList.map(m => m.id))
}
module.exports = {
  getUserLogic,
  getUserListLogic,
  getUserDropDownListLogic,
  insertUserLogic,
  updateUserLogic,
  resetPasswordLogic,
  updateUserPasswordLogic,
  batchInsertUserTestLogic,
  asyncUserRolesToCardSystem
};
