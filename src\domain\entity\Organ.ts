import { <PERSON><PERSON><PERSON>, <PERSON>C<PERSON>umn, Column } from "typeorm";

@Entity({ name: "uc_organ" })
export class Organ {
  @PrimaryColumn()
  id: number;

  @Column({ name: "parent_id" })
  parentId: string;

  @Column({ name: "organ_name" })
  name: string;

  @Column({ name: "role_ids" })
  roleIds: string;

  @Column({ name: "path" })
  path: string;

  @Column({ name: "level" })
  level: number;

  @Column({ name: "sort" })
  sort: number;

  @Column({ name: "status" })
  status: number;

  @Column({ name: "remark" })
  remark: string;

  @Column({ name: "create_user" })
  createUser: string;

  @Column({ name: "create_time" })
  createTime: Date;

  @Column({ name: "update_user" })
  update_user: string;

  @Column({ name: "update_time" })
  updateTime: Date;

  @Column({ name: "tid" })
  tid: number;

  @Column({ name: "secret" })
  secret: string;

  @Column({ name: "alarm_server" })
  alarmServer: string;
}
